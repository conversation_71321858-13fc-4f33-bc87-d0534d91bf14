  # Required Environment Variables (set in Bitbucket Repository Settings > Repository variables):
  #
  # For Bitbucket Downloads deployment:
  # - BITBUCKET_USERNAME: Your Bitbucket username
  # - BITBUCKET_APP_PASSWORD: App password with repository write permissions.

  # For SSH deployment to remote server in Self Hosted: Linux Shell Runner environment using rsync. As pipe only work on docker environment.
  # For Managing Private, Public and Known Hosts for fingerprinting using SSH Keys features of Bitbucket:
  #
  # Set in Bitbucket Repository Settings > Repository variables:
  # - REMOTE_SERVER_HOST: IP address or hostname of your remote server
  # - REMOTE_USER: Username for SSH connection to remote server
  # - REMOTE_PATH: Full path where the WAR file should be deployed on remote server (e.g., /opt/tomcat/webapps/)
  #
  # base64 -w 0 < ~/.ssh/id_rsa: flag -w 0 disable line wrapping entirely & display base64 encode ASCII value
  # - SSH_PRIVATE_KEY_B64: Private key for SSH connection to remote server with base64 encoded in order to keep proper formatting in repo ENV variables
  #
  # ssh-keyscan <remote host IP> | base64 -w 0: flag -w 0 disable line wrapping entirely & display base64 encode ASCII value
  # - SSH_KNOWN_HOSTS_B64: Contains fingerprint of remote host with base64 encode format in order to keep proper formatting in repo ENV variables

  # Prerequisites on remote server:
  # 1. Java JDK >= 11 installed
  # 2. Apache Tomcat or similar servlet container installed and running
  # 3. SSH key authentication configured
  # 4. Maven >= 3.6.0 installed (optional, for local builds).

  defaults: &self_hosted_runner
    runs-on:
      - self.hosted
      - linux.shell

  pipelines:
    default:
      - step:
          <<: *self_hosted_runner
          name: Pre-Build Cleanup
          script:
            - echo "Cleaning working directory..."
            - rm -rf ./* || true
            - rm -rf ./.??* || true

      - step:
          <<: *self_hosted_runner
          name: Build Java Web Application
          caches:
            - maven
          script:
            - echo "Building Java web application with Maven..."
            - mvn clean compile # Clean and compile the application
            - mvn test # Run unit tests
            - mvn clean package # Clean New war file
            - echo "Build completed successfully"
            - ls -la target/ # List build artifacts
          artifacts:
            - target/*.war

      - step:
          <<: *self_hosted_runner
          name: Deploy WAR to Bitbucket Downloads
          script:
            - echo "Preparing WAR file for Bitbucket Downloads..."
            - cp target/pipeline-testing-app.war ./pipeline-testing-app.war
            - ls -la pipeline-testing-app.war
            - echo "Uploading WAR file to Bitbucket Downloads..."
            - curl -X POST "https://api.bitbucket.org/2.0/repositories/$BITBUCKET_REPO_OWNER/$BITBUCKET_REPO_SLUG/downloads" -u "$BITBUCKET_USERNAME:$BITBUCKET_APP_PASSWORD" -F files=@pipeline-testing-app.war
            - echo "WAR file uploaded successfully"
          artifacts:
            - pipeline-testing-app.war

      - step:
          <<: *self_hosted_runner
          name: Deploy WAR to remote server
          script:
            - echo "Start SSH Key Setup"
            - echo "Ensuring .ssh directory exists..."
            - mkdir -p ~/.ssh
            - chmod 700 ~/.ssh # Ensure .ssh directory has correct permissions

            - echo "Configuring SSH private key..."
            - echo $SSH_PRIVATE_KEY_B64 | base64 --decode > ~/.ssh/id_rsa
            - chmod 600 ~/.ssh/id_rsa

            - echo "Configuring known hosts..."
            - echo $SSH_KNOWN_HOSTS_B64 | base64 --decode > ~/.ssh/known_hosts
            - chmod 600 ~/.ssh/known_hosts

            - echo "--- End SSH Key Setup ---"

            - echo "Deploying WAR file to remote server..."
            - echo "Target path: $REMOTE_PATH"
            - scp -i ~/.ssh/id_rsa target/pipeline-testing-app.war $REMOTE_USER@$REMOTE_SERVER_HOST:$REMOTE_PATH
            - echo "WAR file deployed successfully"

      - step:
          <<: *self_hosted_runner
          name: Post-Build Cleanup
          script:
            - echo "Removing build artifacts and sensitive files..."
            - rm -rf target/
            - rm -f pipeline-testing-app.war
            - rm -f ~/.ssh/id_rsa
            - rm -f ~/.ssh/known_hosts
            - echo "Cleanup completed successfully"
